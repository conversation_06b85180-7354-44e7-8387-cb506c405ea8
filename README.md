# Academic Organizer Website

A comprehensive web application for students to organize their academic life, including document storage, notes management, assignment tracking, and syllabus progress monitoring.

## Features

### 🔐 User Authentication
- Secure user registration and login
- Session management
- Password hashing

### 📁 Document Management
- Upload and categorize documents (Personal, College, Certificates, etc.)
- Secure file storage
- Download and delete functionality
- Support for multiple file formats (PDF, images, documents)

### 📝 Notes Management
- Create, edit, and delete notes
- Organize by subject or tags
- Search functionality
- Rich text editing

### 📋 Assignment Tracker
- Add assignments with due dates
- Mark as pending/completed
- Deadline reminders
- Priority levels
- Subject-wise organization

### 📚 Syllabus Progress Tracker
- Add syllabus topics for each subject
- Track completion progress
- Visual progress bars
- Subject-wise completion percentage

### 📊 Dashboard
- Overview of all features
- Upcoming deadlines
- Recent activities
- Progress summaries

## Tech Stack

- **Frontend**: HTML5, CSS3, JavaScript
- **Backend**: Python Flask
- **Database**: SQLite
- **Authentication**: Flask-Login
- **File Handling**: Werkzeug

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python app.py
   ```
4. Open your browser and navigate to `http://localhost:5000`

## Project Structure

```
academic-organizer/
├── app.py              # Main Flask application
├── config.py           # Configuration settings
├── models.py           # Database models
├── routes.py           # Application routes
├── requirements.txt    # Python dependencies
├── templates/          # HTML templates
├── static/            # CSS, JS, and images
└── uploads/           # User uploaded files
```

## Usage

1. Register a new account or login
2. Access your personal dashboard
3. Upload documents and organize them by category
4. Create and manage notes by subject
5. Add assignments and track deadlines
6. Monitor syllabus progress with visual indicators

## Security Features

- Password hashing with Werkzeug
- Secure file uploads with validation
- Session management
- CSRF protection
- File size limitations

## Future Enhancements

- Calendar integration
- Email notifications
- Cloud storage integration
- Mobile app
- Collaborative features
- Advanced analytics
