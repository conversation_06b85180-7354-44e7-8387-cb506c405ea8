{% extends "base.html" %}

{% block title %}Assignments - Academic Organizer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-tasks me-2"></i>Assignments
    </h1>
    <a href="{{ url_for('add_assignment') }}" class="btn btn-warning">
        <i class="fas fa-plus me-2"></i>Add Assignment
    </a>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Filter by Status:</label>
                <select class="form-select" id="statusFilter" onchange="filterAssignments()">
                    <option value="all" {% if selected_status == 'all' %}selected{% endif %}>All Status</option>
                    <option value="Pending" {% if selected_status == 'Pending' %}selected{% endif %}>Pending</option>
                    <option value="Completed" {% if selected_status == 'Completed' %}selected{% endif %}>Completed</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="subjectFilter" class="form-label">Filter by Subject:</label>
                <select class="form-select" id="subjectFilter" onchange="filterAssignments()">
                    <option value="all" {% if selected_subject == 'all' %}selected{% endif %}>All Subjects</option>
                    {% for subject in subjects %}
                        <option value="{{ subject }}" {% if selected_subject == subject %}selected{% endif %}>{{ subject }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Quick Actions:</label>
                <div class="btn-group w-100" role="group">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="showUpcoming()">
                        <i class="fas fa-clock me-1"></i>Upcoming
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="showOverdue()">
                        <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                    </button>
                </div>
            </div>
            <div class="col-md-2 text-end">
                <small class="text-muted">Total: {{ assignments|length }} assignments</small>
            </div>
        </div>
    </div>
</div>

<!-- Assignments List -->
{% if assignments %}
<div class="row">
    {% for assignment in assignments %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100 {% if assignment.is_overdue() %}border-danger{% elif assignment.days_until_due() <= 3 %}border-warning{% else %}border-success{% endif %}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-title mb-0">{{ assignment.title }}</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('edit_assignment', assignment_id=assignment.id) }}">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('toggle_assignment', assignment_id=assignment.id) }}">
                                <i class="fas fa-{% if assignment.status == 'Pending' %}check{% else %}undo{% endif %} me-2"></i>
                                Mark as {% if assignment.status == 'Pending' %}Completed{% else %}Pending{% endif %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteAssignment({{ assignment.id }}, '{{ assignment.title }}')">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a></li>
                        </ul>
                    </div>
                </div>
                
                <p class="card-text">
                    <small class="text-muted">
                        <i class="fas fa-book me-1"></i>{{ assignment.subject }}<br>
                        <i class="fas fa-calendar me-1"></i>Due: {{ assignment.due_date.strftime('%b %d, %Y') }}<br>
                        <i class="fas fa-flag me-1"></i>Priority: 
                        <span class="{% if assignment.priority == 'High' %}text-danger{% elif assignment.priority == 'Medium' %}text-warning{% else %}text-success{% endif %}">
                            {{ assignment.priority }}
                        </span>
                    </small>
                </p>
                
                {% if assignment.description %}
                <p class="card-text">
                    {{ assignment.description[:100] }}{% if assignment.description|length > 100 %}...{% endif %}
                </p>
                {% endif %}
                
                <!-- Status and Due Date Indicators -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        {% if assignment.status == 'Completed' %}
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Completed
                            </span>
                        {% elif assignment.is_overdue() %}
                            <span class="badge bg-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                            </span>
                        {% elif assignment.days_until_due() <= 3 %}
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>{{ assignment.days_until_due() }} days left
                            </span>
                        {% else %}
                            <span class="badge bg-info">
                                <i class="fas fa-calendar me-1"></i>{{ assignment.days_until_due() }} days left
                            </span>
                        {% endif %}
                    </div>
                    
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('toggle_assignment', assignment_id=assignment.id) }}" 
                           class="btn btn-sm {% if assignment.status == 'Pending' %}btn-success{% else %}btn-warning{% endif %}">
                            <i class="fas fa-{% if assignment.status == 'Pending' %}check{% else %}undo{% endif %}"></i>
                        </a>
                        <a href="{{ url_for('edit_assignment', assignment_id=assignment.id) }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-tasks fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">No assignments found</h4>
    <p class="text-muted">Add your first assignment to start tracking deadlines!</p>
    <a href="{{ url_for('add_assignment') }}" class="btn btn-warning">
        <i class="fas fa-plus me-2"></i>Add Assignment
    </a>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the assignment <strong id="deleteAssignmentTitle"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteConfirmBtn" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>Delete
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function filterAssignments() {
    const status = document.getElementById('statusFilter').value;
    const subject = document.getElementById('subjectFilter').value;
    window.location.href = `{{ url_for('assignments') }}?status=${status}&subject=${subject}`;
}

function showUpcoming() {
    // Show assignments due in next 7 days
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        const badge = card.querySelector('.badge');
        if (badge && (badge.textContent.includes('days left') || badge.textContent.includes('Overdue'))) {
            card.parentElement.style.display = 'block';
        } else {
            card.parentElement.style.display = 'none';
        }
    });
}

function showOverdue() {
    // Show only overdue assignments
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        const badge = card.querySelector('.badge');
        if (badge && badge.textContent.includes('Overdue')) {
            card.parentElement.style.display = 'block';
        } else {
            card.parentElement.style.display = 'none';
        }
    });
}

function confirmDeleteAssignment(assignmentId, assignmentTitle) {
    document.getElementById('deleteAssignmentTitle').textContent = assignmentTitle;
    document.getElementById('deleteConfirmBtn').href = `{{ url_for('delete_assignment', assignment_id=0) }}`.replace('0', assignmentId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Add visual indicators for priority
document.addEventListener('DOMContentLoaded', function() {
    const priorityElements = document.querySelectorAll('.card-text small');
    priorityElements.forEach(element => {
        if (element.textContent.includes('High')) {
            element.closest('.card').classList.add('border-danger');
        } else if (element.textContent.includes('Medium')) {
            element.closest('.card').classList.add('border-warning');
        }
    });
});
</script>
{% endblock %}
