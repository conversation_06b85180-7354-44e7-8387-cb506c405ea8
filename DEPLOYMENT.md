# Academic Organizer - Deployment Guide

## Table of Contents
1. [Local Development Setup](#local-development-setup)
2. [Production Deployment](#production-deployment)
3. [Environment Configuration](#environment-configuration)
4. [Database Setup](#database-setup)
5. [Security Considerations](#security-considerations)
6. [Maintenance & Updates](#maintenance--updates)

## Local Development Setup

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)
- Git (optional, for version control)

### Quick Start
```bash
# Clone or download the project
git clone <repository-url>
cd academic-organizer

# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run the application
python app.py
```

### Testing the Installation
```bash
# Run the test suite
python test_app.py

# The test will verify:
# - All imports work correctly
# - Database tables are created
# - Routes are registered
# - Templates exist
# - Static files are present
```

## Production Deployment

### Option 1: Using Gunicorn (Recommended for Linux/macOS)

1. **Install Gunicorn**
```bash
pip install gunicorn
```

2. **Create WSGI entry point** (`wsgi.py`):
```python
from app import app

if __name__ == "__main__":
    app.run()
```

3. **Run with Gunicorn**
```bash
gunicorn --bind 0.0.0.0:8000 wsgi:app
```

### Option 2: Using Waitress (Cross-platform)

1. **Install Waitress**
```bash
pip install waitress
```

2. **Create production runner** (`run_production.py`):
```python
from waitress import serve
from app import app

if __name__ == '__main__':
    serve(app, host='0.0.0.0', port=8000)
```

3. **Run the server**
```bash
python run_production.py
```

### Option 3: Docker Deployment

1. **Create Dockerfile**:
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "app.py"]
```

2. **Build and run**:
```bash
docker build -t academic-organizer .
docker run -p 5000:5000 academic-organizer
```

## Environment Configuration

### Environment Variables
Create a `.env` file for production settings:

```env
# Security
SECRET_KEY=your-super-secret-key-here-change-this-in-production
FLASK_ENV=production

# Database
DATABASE_URL=sqlite:///academic_organizer.db

# File Upload
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Server
HOST=0.0.0.0
PORT=5000
```

### Configuration Class
Update `config.py` for different environments:

```python
import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///academic_organizer.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16777216))
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
```

## Database Setup

### SQLite (Default)
- Automatically created on first run
- Stored in `instance/academic_organizer.db`
- Suitable for single-user or small-scale deployments

### PostgreSQL (Production Recommended)
1. **Install PostgreSQL adapter**:
```bash
pip install psycopg2-binary
```

2. **Update DATABASE_URL**:
```env
DATABASE_URL=postgresql://username:password@localhost/academic_organizer
```

3. **Create database**:
```sql
CREATE DATABASE academic_organizer;
CREATE USER academic_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE academic_organizer TO academic_user;
```

### MySQL
1. **Install MySQL adapter**:
```bash
pip install PyMySQL
```

2. **Update DATABASE_URL**:
```env
DATABASE_URL=mysql+pymysql://username:password@localhost/academic_organizer
```

## Security Considerations

### Production Security Checklist

1. **Change Default Secret Key**
```python
# Generate a secure secret key
import secrets
print(secrets.token_hex(32))
```

2. **Use HTTPS**
- Obtain SSL certificate
- Configure reverse proxy (Nginx/Apache)
- Redirect HTTP to HTTPS

3. **File Upload Security**
- Validate file types
- Scan for malware
- Limit file sizes
- Store uploads outside web root

4. **Database Security**
- Use strong passwords
- Enable SSL connections
- Regular backups
- Limit database user permissions

5. **Server Security**
- Keep OS updated
- Use firewall
- Monitor logs
- Regular security audits

### Nginx Configuration Example
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /path/to/academic-organizer/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Maintenance & Updates

### Regular Maintenance Tasks

1. **Database Backups**
```bash
# SQLite backup
cp instance/academic_organizer.db backups/academic_organizer_$(date +%Y%m%d).db

# PostgreSQL backup
pg_dump academic_organizer > backups/academic_organizer_$(date +%Y%m%d).sql
```

2. **Log Rotation**
```bash
# Setup logrotate for application logs
sudo nano /etc/logrotate.d/academic-organizer
```

3. **Update Dependencies**
```bash
# Check for outdated packages
pip list --outdated

# Update packages
pip install --upgrade package_name

# Update requirements.txt
pip freeze > requirements.txt
```

### Monitoring

1. **Application Health Check**
```python
# Add to routes.py
@app.route('/health')
def health_check():
    return {'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()}
```

2. **Log Monitoring**
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/academic_organizer.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('Academic Organizer startup')
```

### Performance Optimization

1. **Static File Serving**
- Use CDN for static assets
- Enable gzip compression
- Set proper cache headers

2. **Database Optimization**
- Add indexes for frequently queried columns
- Regular VACUUM for SQLite
- Connection pooling for PostgreSQL/MySQL

3. **Application Optimization**
- Enable Flask caching
- Optimize database queries
- Use pagination for large datasets

### Troubleshooting Common Issues

1. **Database Connection Errors**
- Check database server status
- Verify connection string
- Check user permissions

2. **File Upload Issues**
- Verify upload directory permissions
- Check disk space
- Validate file size limits

3. **Performance Issues**
- Monitor server resources
- Check database query performance
- Review application logs

### Scaling Considerations

1. **Horizontal Scaling**
- Use load balancer
- Session storage (Redis/Memcached)
- Shared file storage

2. **Vertical Scaling**
- Increase server resources
- Optimize database configuration
- Use application profiling

---

## Quick Deployment Commands

### Development
```bash
python app.py
```

### Production (Gunicorn)
```bash
gunicorn --bind 0.0.0.0:8000 --workers 4 wsgi:app
```

### Production (Waitress)
```bash
python run_production.py
```

### Docker
```bash
docker build -t academic-organizer .
docker run -d -p 8000:5000 --name academic-organizer academic-organizer
```

For more detailed deployment instructions, consult the documentation for your specific hosting platform (AWS, Heroku, DigitalOcean, etc.).
