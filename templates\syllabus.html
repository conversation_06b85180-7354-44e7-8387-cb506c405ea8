{% extends "base.html" %}

{% block title %}Syllabus - Academic Organizer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-book me-2"></i>Syllabus Progress
    </h1>
    <a href="{{ url_for('add_syllabus') }}" class="btn btn-info">
        <i class="fas fa-plus me-2"></i>Add Topic
    </a>
</div>

<!-- Progress Overview -->
{% if subjects %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-chart-line me-2"></i>Subject Progress Overview
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for subject in subjects %}
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <strong>{{ subject }}</strong>
                    <small class="text-muted">{{ "%.0f"|format(subject_progress[subject]) }}%</small>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar 
                        {% if subject_progress[subject] >= 80 %}bg-success
                        {% elif subject_progress[subject] >= 50 %}bg-info
                        {% elif subject_progress[subject] >= 25 %}bg-warning
                        {% else %}bg-danger{% endif %}" 
                        role="progressbar" 
                        style="width: {{ subject_progress[subject] }}%"
                        aria-valuenow="{{ subject_progress[subject] }}" 
                        aria-valuemin="0" 
                        aria-valuemax="100">
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label for="subjectFilter" class="form-label">Filter by Subject:</label>
                <select class="form-select" id="subjectFilter" onchange="filterSyllabus()">
                    <option value="all" {% if selected_subject == 'all' %}selected{% endif %}>All Subjects</option>
                    {% for subject in subjects %}
                        <option value="{{ subject }}" {% if selected_subject == subject %}selected{% endif %}>{{ subject }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Filter by Status:</label>
                <select class="form-select" id="statusFilter" onchange="filterSyllabus()">
                    <option value="all" {% if selected_status == 'all' %}selected{% endif %}>All Status</option>
                    <option value="Not Started" {% if selected_status == 'Not Started' %}selected{% endif %}>Not Started</option>
                    <option value="In Progress" {% if selected_status == 'In Progress' %}selected{% endif %}>In Progress</option>
                    <option value="Completed" {% if selected_status == 'Completed' %}selected{% endif %}>Completed</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Quick Actions:</label>
                <div class="btn-group w-100" role="group">
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="showInProgress()">
                        <i class="fas fa-clock me-1"></i>In Progress
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="showCompleted()">
                        <i class="fas fa-check me-1"></i>Completed
                    </button>
                </div>
            </div>
            <div class="col-md-2 text-end">
                <small class="text-muted">Total: {{ syllabus_items|length }} topics</small>
            </div>
        </div>
    </div>
</div>

<!-- Syllabus Items -->
{% if syllabus_items %}
<div class="row">
    {% for item in syllabus_items %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100 border-left-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-title mb-0">{{ item.topic }}</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('edit_syllabus', syllabus_id=item.id) }}">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('toggle_syllabus', syllabus_id=item.id) }}">
                                <i class="fas fa-{% if item.status == 'Completed' %}undo{% elif item.status == 'Not Started' %}play{% else %}check{% endif %} me-2"></i>
                                {% if item.status == 'Completed' %}Mark as Not Started
                                {% elif item.status == 'Not Started' %}Mark as In Progress
                                {% else %}Mark as Completed{% endif %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteSyllabus({{ item.id }}, '{{ item.topic }}')">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a></li>
                        </ul>
                    </div>
                </div>
                
                <p class="card-text">
                    <small class="text-muted">
                        <i class="fas fa-book me-1"></i>{{ item.subject }}<br>
                        <i class="fas fa-calendar me-1"></i>{{ item.created_at.strftime('%b %d, %Y') }}
                    </small>
                </p>
                
                {% if item.description %}
                <p class="card-text">
                    {{ item.description[:100] }}{% if item.description|length > 100 %}...{% endif %}
                </p>
                {% endif %}
                
                <!-- Status Badge and Progress -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        {% if item.status == 'Completed' %}
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Completed
                            </span>
                        {% elif item.status == 'In Progress' %}
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>In Progress
                            </span>
                        {% else %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-pause me-1"></i>Not Started
                            </span>
                        {% endif %}
                        
                        {% if item.completed_at %}
                        <br><small class="text-muted">Completed: {{ item.completed_at.strftime('%b %d') }}</small>
                        {% endif %}
                    </div>
                    
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm 
                            {% if item.status == 'Completed' %}btn-success
                            {% elif item.status == 'In Progress' %}btn-warning
                            {% else %}btn-secondary{% endif %}" 
                            onclick="toggleSyllabusStatus({{ item.id }})">
                            <i class="fas fa-{% if item.status == 'Completed' %}undo{% elif item.status == 'Not Started' %}play{% else %}check{% endif %}"></i>
                        </button>
                        <a href="{{ url_for('edit_syllabus', syllabus_id=item.id) }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-book fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">No syllabus topics found</h4>
    <p class="text-muted">Add your first syllabus topic to start tracking progress!</p>
    <a href="{{ url_for('add_syllabus') }}" class="btn btn-info">
        <i class="fas fa-plus me-2"></i>Add Topic
    </a>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the syllabus topic <strong id="deleteSyllabusTitle"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteConfirmBtn" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>Delete
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function filterSyllabus() {
    const subject = document.getElementById('subjectFilter').value;
    const status = document.getElementById('statusFilter').value;
    window.location.href = `{{ url_for('syllabus') }}?subject=${subject}&status=${status}`;
}

function showInProgress() {
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        const badge = card.querySelector('.badge');
        if (badge && badge.textContent.includes('In Progress')) {
            card.parentElement.style.display = 'block';
        } else {
            card.parentElement.style.display = 'none';
        }
    });
}

function showCompleted() {
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        const badge = card.querySelector('.badge');
        if (badge && badge.textContent.includes('Completed')) {
            card.parentElement.style.display = 'block';
        } else {
            card.parentElement.style.display = 'none';
        }
    });
}

function toggleSyllabusStatus(syllabusId) {
    window.location.href = `{{ url_for('toggle_syllabus', syllabus_id=0) }}`.replace('0', syllabusId);
}

function confirmDeleteSyllabus(syllabusId, syllabusTitle) {
    document.getElementById('deleteSyllabusTitle').textContent = syllabusTitle;
    document.getElementById('deleteConfirmBtn').href = `{{ url_for('delete_syllabus', syllabus_id=0) }}`.replace('0', syllabusId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Animate progress bars on page load
document.addEventListener('DOMContentLoaded', function() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(function(bar) {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = targetWidth;
        }, 100);
    });
});
</script>
{% endblock %}
