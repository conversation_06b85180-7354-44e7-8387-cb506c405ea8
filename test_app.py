#!/usr/bin/env python3
"""
Test script for Academic Organizer Website
"""

import sys
import os

def test_imports():
    """Test if all modules can be imported successfully"""
    try:
        print("Testing imports...")
        from app import app
        print("✓ App imported successfully")
        
        from models import db, User, Document, Note, Assignment, SyllabusItem
        print("✓ Models imported successfully")
        
        import routes
        print("✓ Routes imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_database():
    """Test database creation and basic operations"""
    try:
        print("\nTesting database...")
        from app import app
        from models import db, User
        
        with app.app_context():
            # Test database tables creation
            db.create_all()
            print("✓ Database tables created successfully")
            
            # Test user creation
            test_user = User(
                username='testuser',
                email='<EMAIL>',
                first_name='Test',
                last_name='User'
            )
            test_user.set_password('testpassword')
            
            # Check if user already exists
            existing_user = User.query.filter_by(username='testuser').first()
            if not existing_user:
                db.session.add(test_user)
                db.session.commit()
                print("✓ Test user created successfully")
            else:
                print("✓ Test user already exists")
            
            # Test password verification
            user = User.query.filter_by(username='testuser').first()
            if user and user.check_password('testpassword'):
                print("✓ Password verification works")
            else:
                print("✗ Password verification failed")
                
        return True
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_routes():
    """Test if routes are properly registered"""
    try:
        print("\nTesting routes...")
        from app import app
        
        with app.app_context():
            # Get all registered routes
            routes = []
            for rule in app.url_map.iter_rules():
                routes.append(rule.endpoint)
            
            expected_routes = [
                'index', 'login', 'register', 'logout', 'dashboard',
                'documents', 'upload_document', 'notes', 'add_note',
                'assignments', 'add_assignment', 'syllabus', 'add_syllabus'
            ]
            
            missing_routes = []
            for route in expected_routes:
                if route not in routes:
                    missing_routes.append(route)
            
            if not missing_routes:
                print(f"✓ All {len(expected_routes)} expected routes are registered")
            else:
                print(f"✗ Missing routes: {missing_routes}")
                
            print(f"✓ Total routes registered: {len(routes)}")
            
        return len(missing_routes) == 0
    except Exception as e:
        print(f"✗ Routes error: {e}")
        return False

def test_templates():
    """Test if all required templates exist"""
    try:
        print("\nTesting templates...")
        template_dir = 'templates'
        
        required_templates = [
            'base.html', 'index.html', 'login.html', 'register.html',
            'dashboard.html', 'documents.html', 'notes.html', 'add_note.html',
            'assignments.html', 'add_assignment.html', 'syllabus.html', 'add_syllabus.html'
        ]
        
        missing_templates = []
        for template in required_templates:
            template_path = os.path.join(template_dir, template)
            if not os.path.exists(template_path):
                missing_templates.append(template)
        
        if not missing_templates:
            print(f"✓ All {len(required_templates)} required templates exist")
        else:
            print(f"✗ Missing templates: {missing_templates}")
            
        return len(missing_templates) == 0
    except Exception as e:
        print(f"✗ Templates error: {e}")
        return False

def test_static_files():
    """Test if static files exist"""
    try:
        print("\nTesting static files...")
        
        static_files = [
            'static/css/style.css',
            'static/js/main.js'
        ]
        
        missing_files = []
        for file_path in static_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if not missing_files:
            print(f"✓ All {len(static_files)} static files exist")
        else:
            print(f"✗ Missing static files: {missing_files}")
            
        return len(missing_files) == 0
    except Exception as e:
        print(f"✗ Static files error: {e}")
        return False

def run_flask_server():
    """Start the Flask development server"""
    try:
        print("\n" + "="*50)
        print("STARTING FLASK DEVELOPMENT SERVER")
        print("="*50)
        print("Server will start at: http://127.0.0.1:5000")
        print("Press Ctrl+C to stop the server")
        print("="*50)
        
        from app import app
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\n✗ Server error: {e}")

def main():
    """Run all tests"""
    print("Academic Organizer Website - Test Suite")
    print("="*50)
    
    tests = [
        test_imports,
        test_database,
        test_routes,
        test_templates,
        test_static_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The application is ready to run.")
        
        # Ask user if they want to start the server
        response = input("\nWould you like to start the Flask server? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            run_flask_server()
    else:
        print("✗ Some tests failed. Please fix the issues before running the application.")
        sys.exit(1)

if __name__ == '__main__':
    main()
