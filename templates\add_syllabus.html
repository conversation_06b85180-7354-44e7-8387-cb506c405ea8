{% extends "base.html" %}

{% block title %}Add Syllabus Topic - Academic Organizer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>Add Syllabus Topic
    </h1>
    <a href="{{ url_for('syllabus') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Syllabus
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-book me-2"></i>Topic Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="topic" class="form-label">Topic Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="topic" name="topic" required 
                                   placeholder="Enter topic name...">
                            <div class="invalid-feedback">
                                Please provide a topic name.
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required 
                                   placeholder="e.g., Mathematics, Physics..." list="subjectList">
                            <datalist id="subjectList">
                                <option value="Mathematics">
                                <option value="Physics">
                                <option value="Chemistry">
                                <option value="Biology">
                                <option value="Computer Science">
                                <option value="English">
                                <option value="History">
                                <option value="Geography">
                                <option value="Economics">
                                <option value="Psychology">
                            </datalist>
                            <div class="invalid-feedback">
                                Please provide a subject.
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="Enter topic description, key points, or learning objectives..."></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span> characters
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('syllabus') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-2"></i>Save Topic
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Syllabus Management Tips Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Syllabus Management Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Topic Organization:</h6>
                        <ul class="mb-3">
                            <li>Break down large chapters into smaller topics</li>
                            <li>Use descriptive topic names for easy identification</li>
                            <li>Group related topics under the same subject</li>
                            <li>Include subtopics in the description if needed</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Progress Tracking:</h6>
                        <ul class="mb-0">
                            <li>Mark topics as "In Progress" when you start studying</li>
                            <li>Complete topics only when fully understood</li>
                            <li>Review completed topics regularly</li>
                            <li>Use descriptions to note important concepts</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Add Multiple Topics Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-magic me-2"></i>Quick Add Multiple Topics
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">Add multiple topics at once by entering them one per line:</p>
                <div class="mb-3">
                    <label for="bulkTopics" class="form-label">Bulk Topics (Optional)</label>
                    <textarea class="form-control" id="bulkTopics" rows="4" 
                              placeholder="Enter one topic per line:&#10;Introduction to Algebra&#10;Linear Equations&#10;Quadratic Equations&#10;Polynomials"></textarea>
                </div>
                <button type="button" class="btn btn-outline-info" onclick="processBulkTopics()">
                    <i class="fas fa-list me-2"></i>Process Bulk Topics
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Character counter for description textarea
document.getElementById('description').addEventListener('input', function() {
    const charCount = this.value.length;
    document.getElementById('charCount').textContent = charCount;
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-save draft functionality
let autoSaveTimeout;

function autoSave() {
    const formData = {
        topic: document.getElementById('topic').value,
        subject: document.getElementById('subject').value,
        description: document.getElementById('description').value
    };
    
    localStorage.setItem('syllabus_draft', JSON.stringify(formData));
}

// Auto-save every 30 seconds
['topic', 'subject', 'description'].forEach(fieldId => {
    document.getElementById(fieldId).addEventListener('input', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(autoSave, 30000);
    });
});

// Load draft on page load
document.addEventListener('DOMContentLoaded', function() {
    const draft = localStorage.getItem('syllabus_draft');
    if (draft) {
        const formData = JSON.parse(draft);
        if (confirm('A draft was found. Would you like to restore it?')) {
            document.getElementById('topic').value = formData.topic || '';
            document.getElementById('subject').value = formData.subject || '';
            document.getElementById('description').value = formData.description || '';
            
            // Update character count
            const charCount = document.getElementById('description').value.length;
            document.getElementById('charCount').textContent = charCount;
        }
    }
});

// Clear draft on successful form submission
document.querySelector('form').addEventListener('submit', function() {
    localStorage.removeItem('syllabus_draft');
});

// Bulk topics processing
function processBulkTopics() {
    const bulkText = document.getElementById('bulkTopics').value.trim();
    if (!bulkText) {
        alert('Please enter topics to process.');
        return;
    }
    
    const topics = bulkText.split('\n').filter(topic => topic.trim());
    const subject = document.getElementById('subject').value;
    
    if (!subject) {
        alert('Please enter a subject first.');
        document.getElementById('subject').focus();
        return;
    }
    
    if (topics.length === 0) {
        alert('No valid topics found.');
        return;
    }
    
    if (confirm(`This will create ${topics.length} topics for the subject "${subject}". Continue?`)) {
        // Create a form for bulk submission
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("add_syllabus") }}';
        
        // Add CSRF token if available
        const csrfToken = document.querySelector('input[name="csrf_token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.value;
            form.appendChild(csrfInput);
        }
        
        // Add bulk topics data
        const bulkInput = document.createElement('input');
        bulkInput.type = 'hidden';
        bulkInput.name = 'bulk_topics';
        bulkInput.value = JSON.stringify({
            subject: subject,
            topics: topics
        });
        form.appendChild(bulkInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Subject auto-completion enhancement
document.getElementById('subject').addEventListener('input', function() {
    const value = this.value.toLowerCase();
    const datalist = document.getElementById('subjectList');
    const options = datalist.querySelectorAll('option');
    
    // Hide/show options based on input
    options.forEach(option => {
        if (option.value.toLowerCase().includes(value)) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
