# Academic Organizer - User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [User Registration & Login](#user-registration--login)
3. [Dashboard Overview](#dashboard-overview)
4. [Document Management](#document-management)
5. [Notes Management](#notes-management)
6. [Assignment Tracker](#assignment-tracker)
7. [Syllabus Progress](#syllabus-progress)
8. [Tips & Best Practices](#tips--best-practices)

## Getting Started

### System Requirements
- Python 3.8 or higher
- Web browser (Chrome, Firefox, Safari, Edge)
- 100MB free disk space

### Installation
1. Download or clone the project files
2. Open terminal/command prompt in the project directory
3. Install dependencies: `pip install -r requirements.txt`
4. Run the application: `python app.py`
5. Open your browser and go to `http://127.0.0.1:5000`

## User Registration & Login

### Creating an Account
1. Click "Get Started" or "Register here" on the homepage
2. Fill in your details:
   - First Name and Last Name
   - Username (must be unique)
   - Email address
   - Password (minimum 6 characters)
3. Click "Create Account"
4. You'll be redirected to the login page

### Logging In
1. Enter your username and password
2. Click "Login"
3. You'll be taken to your personal dashboard

## Dashboard Overview

The dashboard is your central hub showing:

### Statistics Cards
- **Documents**: Total uploaded files
- **Notes**: Number of notes created
- **Assignments**: Completed vs total assignments
- **Syllabus**: Overall completion percentage

### Quick Access Sections
- **Recent Documents**: Last 5 uploaded files
- **Upcoming Assignments**: Next 5 pending assignments with due dates
- **Recent Notes**: Last 5 created or updated notes

### Color Coding
- 🔴 **Red**: Overdue assignments, urgent items
- 🟡 **Yellow**: Due soon (within 3 days)
- 🟢 **Green**: Completed items, good progress
- 🔵 **Blue**: General information, neutral status

## Document Management

### Uploading Documents
1. Go to "Documents" section
2. Click "Upload Document"
3. Select your file (PDF, DOC, DOCX, TXT, images)
4. Choose a category:
   - Personal
   - College
   - Certificates
   - ID Cards
   - Others
5. Add optional description
6. Click "Upload"

### Managing Documents
- **Download**: Click the download icon to save file locally
- **Delete**: Click trash icon to permanently remove
- **Filter**: Use category dropdown to filter documents
- **View Details**: See file size, upload date, and description

### Supported File Types
- Documents: PDF, DOC, DOCX, TXT
- Images: PNG, JPG, JPEG, GIF
- Presentations: PPT, PPTX
- Maximum file size: 16MB

## Notes Management

### Creating Notes
1. Go to "Notes" section
2. Click "Add Note"
3. Fill in:
   - **Title**: Descriptive note title
   - **Subject**: Course or topic area
   - **Tags**: Comma-separated keywords (optional)
   - **Content**: Your note content
4. Click "Save Note"

### Organizing Notes
- **Search**: Use the search bar to find notes by title or content
- **Filter by Subject**: Select specific subjects from dropdown
- **Tags**: Use tags for easy categorization and searching
- **Edit**: Click on any note to modify it
- **Delete**: Remove notes you no longer need

### Note Features
- **Auto-save**: Drafts are saved automatically every 30 seconds
- **Character Counter**: Track note length
- **Rich Text**: Format your notes with line breaks and structure
- **Search**: Find notes quickly using the search function

## Assignment Tracker

### Adding Assignments
1. Go to "Assignments" section
2. Click "Add Assignment"
3. Enter details:
   - **Title**: Assignment name
   - **Subject**: Course name
   - **Due Date**: Deadline (cannot be in the past)
   - **Priority**: High, Medium, or Low
   - **Description**: Optional details
4. Click "Save Assignment"

### Managing Assignments
- **Status Toggle**: Mark assignments as completed/pending
- **Edit**: Modify assignment details
- **Delete**: Remove assignments
- **Filter**: By status (all, pending, completed) or subject
- **Quick Actions**: View upcoming or overdue assignments

### Priority System
- **🔴 High**: Critical assignments, due within 3 days
- **🟡 Medium**: Standard assignments with moderate deadlines
- **🟢 Low**: Long-term projects, optional tasks

### Status Indicators
- **⏰ Overdue**: Past due date, needs immediate attention
- **⚠️ Due Soon**: Due within 3 days
- **✅ Completed**: Finished assignments
- **📅 Upcoming**: Future assignments

## Syllabus Progress

### Adding Topics
1. Go to "Syllabus" section
2. Click "Add Topic"
3. Enter:
   - **Topic Name**: Specific topic or chapter
   - **Subject**: Course name
   - **Description**: Learning objectives or key points
4. Click "Save Topic"

### Tracking Progress
- **Status Options**:
  - 🔘 **Not Started**: Haven't begun studying
  - 🟡 **In Progress**: Currently studying
  - ✅ **Completed**: Fully understood and mastered

### Progress Overview
- **Subject Progress Bars**: Visual representation of completion
- **Percentage Tracking**: See exact completion rates
- **Color-coded Progress**:
  - 🔴 Red: 0-25% complete
  - 🟡 Yellow: 25-50% complete
  - 🔵 Blue: 50-80% complete
  - 🟢 Green: 80-100% complete

### Managing Topics
- **Status Toggle**: Click to cycle through Not Started → In Progress → Completed
- **Edit**: Modify topic details
- **Delete**: Remove topics
- **Filter**: By subject or status
- **Bulk Add**: Add multiple topics at once

## Tips & Best Practices

### Document Organization
- Use consistent naming conventions
- Choose appropriate categories
- Add descriptions for important documents
- Regularly clean up unnecessary files

### Effective Note-Taking
- Use descriptive titles
- Organize by subject
- Add relevant tags
- Review and update notes regularly
- Break complex topics into multiple notes

### Assignment Management
- Set realistic due dates
- Use priority levels effectively
- Add detailed descriptions for complex assignments
- Review upcoming assignments daily
- Mark assignments complete promptly

### Syllabus Tracking
- Break large chapters into smaller topics
- Update status regularly as you study
- Use descriptions for key concepts
- Review completed topics periodically
- Set realistic learning goals

### General Tips
- **Regular Updates**: Keep your data current
- **Backup Important Files**: Download critical documents
- **Use Search**: Leverage search functionality to find information quickly
- **Mobile Friendly**: Access from any device with a web browser
- **Security**: Log out when using shared computers

### Keyboard Shortcuts
- **Ctrl+F**: Search within pages
- **Tab**: Navigate between form fields
- **Enter**: Submit forms
- **Esc**: Close modals/dialogs

### Troubleshooting
- **Can't upload file**: Check file size (max 16MB) and format
- **Login issues**: Verify username and password
- **Missing data**: Refresh the page
- **Slow performance**: Clear browser cache

## Support & Feedback

For technical issues or feature requests:
1. Check this user guide first
2. Verify your internet connection
3. Try refreshing the page
4. Clear browser cache if needed

Remember: This is a local application running on your computer. Your data is stored locally and is private to you.

---

**Happy Studying! 📚✨**
