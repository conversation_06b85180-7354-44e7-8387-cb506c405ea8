from flask import render_template, request, redirect, url_for, flash, session, jsonify, send_file
from flask_login import login_user, login_required, logout_user, current_user
from werkzeug.utils import secure_filename
from app import app, db
from models import User, Document, Note, Assignment, SyllabusItem
from datetime import datetime, date
import os
import json

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config.get('ALLOWED_EXTENSIONS', {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'})

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        first_name = request.form['first_name']
        last_name = request.form['last_name']
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists')
            return render_template('register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Email already registered')
            return render_template('register.html')
        
        # Create new user
        user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Registration successful! Please login.')
        return redirect(url_for('login'))
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user, remember=True)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out.')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get recent activities and statistics
    recent_notes = Note.query.filter_by(user_id=current_user.id).order_by(Note.updated_at.desc()).limit(5).all()
    upcoming_assignments = Assignment.query.filter_by(user_id=current_user.id, status='Pending').order_by(Assignment.due_date).limit(5).all()
    recent_documents = Document.query.filter_by(user_id=current_user.id).order_by(Document.upload_date.desc()).limit(5).all()
    
    # Statistics
    total_notes = Note.query.filter_by(user_id=current_user.id).count()
    total_assignments = Assignment.query.filter_by(user_id=current_user.id).count()
    completed_assignments = Assignment.query.filter_by(user_id=current_user.id, status='Completed').count()
    total_documents = Document.query.filter_by(user_id=current_user.id).count()
    
    # Syllabus progress
    total_syllabus = SyllabusItem.query.filter_by(user_id=current_user.id).count()
    completed_syllabus = SyllabusItem.query.filter_by(user_id=current_user.id, status='Completed').count()
    syllabus_progress = (completed_syllabus / total_syllabus * 100) if total_syllabus > 0 else 0
    
    return render_template('dashboard.html',
                         recent_notes=recent_notes,
                         upcoming_assignments=upcoming_assignments,
                         recent_documents=recent_documents,
                         total_notes=total_notes,
                         total_assignments=total_assignments,
                         completed_assignments=completed_assignments,
                         total_documents=total_documents,
                         syllabus_progress=syllabus_progress)

# Document routes
@app.route('/documents')
@login_required
def documents():
    category = request.args.get('category', 'all')
    if category == 'all':
        docs = Document.query.filter_by(user_id=current_user.id).order_by(Document.upload_date.desc()).all()
    else:
        docs = Document.query.filter_by(user_id=current_user.id, category=category).order_by(Document.upload_date.desc()).all()
    
    categories = ['Personal', 'College', 'Certificates', 'ID Cards', 'Others']
    return render_template('documents.html', documents=docs, categories=categories, selected_category=category)

@app.route('/upload_document', methods=['POST'])
@login_required
def upload_document():
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(url_for('documents'))
    
    file = request.files['file']
    category = request.form['category']
    description = request.form.get('description', '')
    
    if file.filename == '':
        flash('No file selected')
        return redirect(url_for('documents'))
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
        filename = timestamp + filename
        
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Create document record
        document = Document(
            user_id=current_user.id,
            filename=filename,
            original_filename=file.filename,
            file_path=file_path,
            category=category,
            file_size=os.path.getsize(file_path),
            file_type=file.filename.rsplit('.', 1)[1].lower(),
            description=description
        )
        
        db.session.add(document)
        db.session.commit()
        
        flash('Document uploaded successfully!')
    else:
        flash('Invalid file type')
    
    return redirect(url_for('documents'))

@app.route('/download_document/<int:doc_id>')
@login_required
def download_document(doc_id):
    document = Document.query.filter_by(id=doc_id, user_id=current_user.id).first_or_404()
    return send_file(document.file_path, as_attachment=True, download_name=document.original_filename)

@app.route('/delete_document/<int:doc_id>')
@login_required
def delete_document(doc_id):
    document = Document.query.filter_by(id=doc_id, user_id=current_user.id).first_or_404()

    # Delete file from filesystem
    if os.path.exists(document.file_path):
        os.remove(document.file_path)

    # Delete from database
    db.session.delete(document)
    db.session.commit()

    flash('Document deleted successfully!')
    return redirect(url_for('documents'))

# Notes routes
@app.route('/notes')
@login_required
def notes():
    subject = request.args.get('subject', 'all')
    search = request.args.get('search', '')

    query = Note.query.filter_by(user_id=current_user.id)

    if subject != 'all':
        query = query.filter_by(subject=subject)

    if search:
        query = query.filter(Note.title.contains(search) | Note.content.contains(search))

    notes = query.order_by(Note.updated_at.desc()).all()

    # Get unique subjects for filter
    subjects = db.session.query(Note.subject).filter_by(user_id=current_user.id).distinct().all()
    subjects = [s[0] for s in subjects]

    return render_template('notes.html', notes=notes, subjects=subjects, selected_subject=subject, search_term=search)

@app.route('/add_note', methods=['GET', 'POST'])
@login_required
def add_note():
    if request.method == 'POST':
        title = request.form['title']
        content = request.form['content']
        subject = request.form['subject']
        tags = request.form.get('tags', '')

        note = Note(
            user_id=current_user.id,
            title=title,
            content=content,
            subject=subject,
            tags=tags
        )

        db.session.add(note)
        db.session.commit()

        flash('Note added successfully!')
        return redirect(url_for('notes'))

    return render_template('add_note.html')

@app.route('/edit_note/<int:note_id>', methods=['GET', 'POST'])
@login_required
def edit_note(note_id):
    note = Note.query.filter_by(id=note_id, user_id=current_user.id).first_or_404()

    if request.method == 'POST':
        note.title = request.form['title']
        note.content = request.form['content']
        note.subject = request.form['subject']
        note.tags = request.form.get('tags', '')
        note.updated_at = datetime.utcnow()

        db.session.commit()

        flash('Note updated successfully!')
        return redirect(url_for('notes'))

    return render_template('edit_note.html', note=note)

@app.route('/delete_note/<int:note_id>')
@login_required
def delete_note(note_id):
    note = Note.query.filter_by(id=note_id, user_id=current_user.id).first_or_404()

    db.session.delete(note)
    db.session.commit()

    flash('Note deleted successfully!')
    return redirect(url_for('notes'))

# Assignment routes
@app.route('/assignments')
@login_required
def assignments():
    status = request.args.get('status', 'all')
    subject = request.args.get('subject', 'all')

    query = Assignment.query.filter_by(user_id=current_user.id)

    if status != 'all':
        query = query.filter_by(status=status)

    if subject != 'all':
        query = query.filter_by(subject=subject)

    assignments = query.order_by(Assignment.due_date).all()

    # Get unique subjects for filter
    subjects = db.session.query(Assignment.subject).filter_by(user_id=current_user.id).distinct().all()
    subjects = [s[0] for s in subjects]

    return render_template('assignments.html', assignments=assignments, subjects=subjects,
                         selected_status=status, selected_subject=subject)

@app.route('/add_assignment', methods=['GET', 'POST'])
@login_required
def add_assignment():
    if request.method == 'POST':
        title = request.form['title']
        description = request.form.get('description', '')
        subject = request.form['subject']
        due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d').date()
        priority = request.form['priority']

        assignment = Assignment(
            user_id=current_user.id,
            title=title,
            description=description,
            subject=subject,
            due_date=due_date,
            priority=priority
        )

        db.session.add(assignment)
        db.session.commit()

        flash('Assignment added successfully!')
        return redirect(url_for('assignments'))

    return render_template('add_assignment.html')

@app.route('/edit_assignment/<int:assignment_id>', methods=['GET', 'POST'])
@login_required
def edit_assignment(assignment_id):
    assignment = Assignment.query.filter_by(id=assignment_id, user_id=current_user.id).first_or_404()

    if request.method == 'POST':
        assignment.title = request.form['title']
        assignment.description = request.form.get('description', '')
        assignment.subject = request.form['subject']
        assignment.due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d').date()
        assignment.priority = request.form['priority']

        db.session.commit()

        flash('Assignment updated successfully!')
        return redirect(url_for('assignments'))

    return render_template('edit_assignment.html', assignment=assignment)

@app.route('/toggle_assignment/<int:assignment_id>')
@login_required
def toggle_assignment(assignment_id):
    assignment = Assignment.query.filter_by(id=assignment_id, user_id=current_user.id).first_or_404()

    if assignment.status == 'Pending':
        assignment.status = 'Completed'
        assignment.completed_at = datetime.utcnow()
    else:
        assignment.status = 'Pending'
        assignment.completed_at = None

    db.session.commit()

    flash(f'Assignment marked as {assignment.status.lower()}!')
    return redirect(url_for('assignments'))

@app.route('/delete_assignment/<int:assignment_id>')
@login_required
def delete_assignment(assignment_id):
    assignment = Assignment.query.filter_by(id=assignment_id, user_id=current_user.id).first_or_404()

    db.session.delete(assignment)
    db.session.commit()

    flash('Assignment deleted successfully!')
    return redirect(url_for('assignments'))

# Syllabus routes
@app.route('/syllabus')
@login_required
def syllabus():
    subject = request.args.get('subject', 'all')
    status = request.args.get('status', 'all')

    query = SyllabusItem.query.filter_by(user_id=current_user.id)

    if subject != 'all':
        query = query.filter_by(subject=subject)

    if status != 'all':
        query = query.filter_by(status=status)

    syllabus_items = query.order_by(SyllabusItem.subject, SyllabusItem.created_at).all()

    # Get unique subjects for filter
    subjects = db.session.query(SyllabusItem.subject).filter_by(user_id=current_user.id).distinct().all()
    subjects = [s[0] for s in subjects]

    # Calculate progress for each subject
    subject_progress = {}
    for subj in subjects:
        total = SyllabusItem.query.filter_by(user_id=current_user.id, subject=subj).count()
        completed = SyllabusItem.query.filter_by(user_id=current_user.id, subject=subj, status='Completed').count()
        subject_progress[subj] = (completed / total * 100) if total > 0 else 0

    return render_template('syllabus.html', syllabus_items=syllabus_items, subjects=subjects,
                         selected_subject=subject, selected_status=status, subject_progress=subject_progress)

@app.route('/add_syllabus', methods=['GET', 'POST'])
@login_required
def add_syllabus():
    if request.method == 'POST':
        subject = request.form['subject']
        topic = request.form['topic']
        description = request.form.get('description', '')

        syllabus_item = SyllabusItem(
            user_id=current_user.id,
            subject=subject,
            topic=topic,
            description=description
        )

        db.session.add(syllabus_item)
        db.session.commit()

        flash('Syllabus item added successfully!')
        return redirect(url_for('syllabus'))

    return render_template('add_syllabus.html')

@app.route('/edit_syllabus/<int:syllabus_id>', methods=['GET', 'POST'])
@login_required
def edit_syllabus(syllabus_id):
    syllabus_item = SyllabusItem.query.filter_by(id=syllabus_id, user_id=current_user.id).first_or_404()

    if request.method == 'POST':
        syllabus_item.subject = request.form['subject']
        syllabus_item.topic = request.form['topic']
        syllabus_item.description = request.form.get('description', '')

        db.session.commit()

        flash('Syllabus item updated successfully!')
        return redirect(url_for('syllabus'))

    return render_template('edit_syllabus.html', syllabus_item=syllabus_item)

@app.route('/toggle_syllabus/<int:syllabus_id>')
@login_required
def toggle_syllabus(syllabus_id):
    syllabus_item = SyllabusItem.query.filter_by(id=syllabus_id, user_id=current_user.id).first_or_404()

    if syllabus_item.status == 'Completed':
        syllabus_item.status = 'Not Started'
        syllabus_item.completed_at = None
    elif syllabus_item.status == 'Not Started':
        syllabus_item.status = 'In Progress'
    else:  # In Progress
        syllabus_item.status = 'Completed'
        syllabus_item.completed_at = datetime.utcnow()

    db.session.commit()

    flash(f'Syllabus item marked as {syllabus_item.status.lower()}!')
    return redirect(url_for('syllabus'))

@app.route('/delete_syllabus/<int:syllabus_id>')
@login_required
def delete_syllabus(syllabus_id):
    syllabus_item = SyllabusItem.query.filter_by(id=syllabus_id, user_id=current_user.id).first_or_404()

    db.session.delete(syllabus_item)
    db.session.commit()

    flash('Syllabus item deleted successfully!')
    return redirect(url_for('syllabus'))
