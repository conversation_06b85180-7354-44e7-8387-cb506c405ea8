{% extends "base.html" %}

{% block title %}Add Assignment - Academic Organizer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>Add New Assignment
    </h1>
    <a href="{{ url_for('assignments') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Assignments
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>Assignment Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Assignment Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required 
                                   placeholder="Enter assignment title...">
                            <div class="invalid-feedback">
                                Please provide an assignment title.
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required 
                                   placeholder="e.g., Mathematics, Physics..." list="subjectList">
                            <datalist id="subjectList">
                                <option value="Mathematics">
                                <option value="Physics">
                                <option value="Chemistry">
                                <option value="Biology">
                                <option value="Computer Science">
                                <option value="English">
                                <option value="History">
                                <option value="Geography">
                                <option value="Economics">
                                <option value="Psychology">
                            </datalist>
                            <div class="invalid-feedback">
                                Please provide a subject.
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="due_date" name="due_date" required>
                            <div class="invalid-feedback">
                                Please provide a due date.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">Select Priority</option>
                                <option value="High">High</option>
                                <option value="Medium" selected>Medium</option>
                                <option value="Low">Low</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a priority level.
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="Enter assignment description, requirements, or notes..."></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span> characters
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('assignments') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>Save Assignment
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Assignment Tips Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Assignment Management Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Priority Guidelines:</h6>
                        <ul class="mb-3">
                            <li><strong class="text-danger">High:</strong> Due within 3 days or critical importance</li>
                            <li><strong class="text-warning">Medium:</strong> Standard assignments with moderate deadlines</li>
                            <li><strong class="text-success">Low:</strong> Long-term projects or optional tasks</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Best Practices:</h6>
                        <ul class="mb-0">
                            <li>Set realistic due dates</li>
                            <li>Break large assignments into smaller tasks</li>
                            <li>Include detailed descriptions for complex assignments</li>
                            <li>Review and update assignments regularly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('due_date').setAttribute('min', today);
    
    // Set default due date to 7 days from now
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    document.getElementById('due_date').value = nextWeek.toISOString().split('T')[0];
});

// Character counter for description textarea
document.getElementById('description').addEventListener('input', function() {
    const charCount = this.value.length;
    document.getElementById('charCount').textContent = charCount;
});

// Due date validation and warnings
document.getElementById('due_date').addEventListener('change', function() {
    const selectedDate = new Date(this.value);
    const today = new Date();
    const diffTime = selectedDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const warningDiv = document.getElementById('dateWarning');
    if (warningDiv) {
        warningDiv.remove();
    }
    
    if (diffDays < 0) {
        this.setCustomValidity('Due date cannot be in the past');
        this.classList.add('is-invalid');
    } else if (diffDays <= 1) {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        
        const warning = document.createElement('div');
        warning.id = 'dateWarning';
        warning.className = 'alert alert-warning mt-2';
        warning.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>This assignment is due very soon! Consider setting a higher priority.';
        this.parentElement.appendChild(warning);
        
        // Auto-set priority to High for urgent assignments
        document.getElementById('priority').value = 'High';
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Priority color coding
document.getElementById('priority').addEventListener('change', function() {
    const prioritySelect = this;
    prioritySelect.className = 'form-select';
    
    if (this.value === 'High') {
        prioritySelect.classList.add('border-danger');
    } else if (this.value === 'Medium') {
        prioritySelect.classList.add('border-warning');
    } else if (this.value === 'Low') {
        prioritySelect.classList.add('border-success');
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-save draft functionality
let autoSaveTimeout;

function autoSave() {
    const formData = {
        title: document.getElementById('title').value,
        subject: document.getElementById('subject').value,
        due_date: document.getElementById('due_date').value,
        priority: document.getElementById('priority').value,
        description: document.getElementById('description').value
    };
    
    localStorage.setItem('assignment_draft', JSON.stringify(formData));
}

// Auto-save every 30 seconds
['title', 'subject', 'due_date', 'priority', 'description'].forEach(fieldId => {
    document.getElementById(fieldId).addEventListener('input', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(autoSave, 30000);
    });
});

// Load draft on page load
document.addEventListener('DOMContentLoaded', function() {
    const draft = localStorage.getItem('assignment_draft');
    if (draft) {
        const formData = JSON.parse(draft);
        if (confirm('A draft was found. Would you like to restore it?')) {
            document.getElementById('title').value = formData.title || '';
            document.getElementById('subject').value = formData.subject || '';
            document.getElementById('due_date').value = formData.due_date || '';
            document.getElementById('priority').value = formData.priority || 'Medium';
            document.getElementById('description').value = formData.description || '';
            
            // Update character count
            const charCount = document.getElementById('description').value.length;
            document.getElementById('charCount').textContent = charCount;
        }
    }
});

// Clear draft on successful form submission
document.querySelector('form').addEventListener('submit', function() {
    localStorage.removeItem('assignment_draft');
});
</script>
{% endblock %}
