{% extends "base.html" %}

{% block title %}Edit Assignment - Academic Organizer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>Edit Assignment
    </h1>
    <a href="{{ url_for('assignments') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Assignments
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>Assignment Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Assignment Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required 
                                   value="{{ assignment.title }}" placeholder="Enter assignment title...">
                            <div class="invalid-feedback">
                                Please provide an assignment title.
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required 
                                   value="{{ assignment.subject }}" placeholder="e.g., Mathematics, Physics..." list="subjectList">
                            <datalist id="subjectList">
                                <option value="Mathematics">
                                <option value="Physics">
                                <option value="Chemistry">
                                <option value="Biology">
                                <option value="Computer Science">
                                <option value="English">
                                <option value="History">
                                <option value="Geography">
                                <option value="Economics">
                                <option value="Psychology">
                            </datalist>
                            <div class="invalid-feedback">
                                Please provide a subject.
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="due_date" name="due_date" required
                                   value="{{ assignment.due_date.strftime('%Y-%m-%d') }}">
                            <div class="invalid-feedback">
                                Please provide a due date.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">Select Priority</option>
                                <option value="High" {% if assignment.priority == 'High' %}selected{% endif %}>High</option>
                                <option value="Medium" {% if assignment.priority == 'Medium' %}selected{% endif %}>Medium</option>
                                <option value="Low" {% if assignment.priority == 'Low' %}selected{% endif %}>Low</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a priority level.
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="Enter assignment description, requirements, or notes...">{{ assignment.description or '' }}</textarea>
                        <div class="form-text">
                            <span id="charCount">{{ (assignment.description or '')|length }}</span> characters
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ url_for('assignments') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <a href="{{ url_for('delete_assignment', assignment_id=assignment.id) }}" class="btn btn-outline-danger ms-2" 
                               onclick="return confirm('Are you sure you want to delete this assignment?')">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>Update Assignment
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Assignment Information Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Assignment Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>Status:</strong> 
                            <span class="badge {% if assignment.status == 'Completed' %}bg-success{% else %}bg-warning{% endif %}">
                                {{ assignment.status }}
                            </span>
                        </p>
                        <p class="mb-2">
                            <strong>Created:</strong> {{ assignment.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                        {% if assignment.completed_at %}
                        <p class="mb-2">
                            <strong>Completed:</strong> {{ assignment.completed_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>Due Date:</strong> {{ assignment.due_date.strftime('%B %d, %Y') }}
                        </p>
                        <p class="mb-2">
                            <strong>Days Until Due:</strong> 
                            {% if assignment.is_overdue() %}
                                <span class="text-danger">Overdue by {{ -assignment.days_until_due() }} days</span>
                            {% elif assignment.days_until_due() == 0 %}
                                <span class="text-warning">Due Today!</span>
                            {% else %}
                                <span class="{% if assignment.days_until_due() <= 3 %}text-warning{% else %}text-success{% endif %}">
                                    {{ assignment.days_until_due() }} days
                                </span>
                            {% endif %}
                        </p>
                        <p class="mb-2">
                            <strong>Priority:</strong> 
                            <span class="{% if assignment.priority == 'High' %}text-danger{% elif assignment.priority == 'Medium' %}text-warning{% else %}text-success{% endif %}">
                                {{ assignment.priority }}
                            </span>
                        </p>
                    </div>
                </div>
                
                {% if assignment.status == 'Pending' %}
                <div class="mt-3">
                    <a href="{{ url_for('toggle_assignment', assignment_id=assignment.id) }}" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Mark as Completed
                    </a>
                </div>
                {% else %}
                <div class="mt-3">
                    <a href="{{ url_for('toggle_assignment', assignment_id=assignment.id) }}" class="btn btn-warning">
                        <i class="fas fa-undo me-2"></i>Mark as Pending
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Assignment Tips Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Assignment Management Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Priority Guidelines:</h6>
                        <ul class="mb-3">
                            <li><strong class="text-danger">High:</strong> Due within 3 days or critical importance</li>
                            <li><strong class="text-warning">Medium:</strong> Standard assignments with moderate deadlines</li>
                            <li><strong class="text-success">Low:</strong> Long-term projects or optional tasks</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Best Practices:</h6>
                        <ul class="mb-0">
                            <li>Update status regularly</li>
                            <li>Adjust priority based on changing circumstances</li>
                            <li>Add detailed descriptions for complex assignments</li>
                            <li>Set realistic due dates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Character counter for description textarea
document.getElementById('description').addEventListener('input', function() {
    const charCount = this.value.length;
    document.getElementById('charCount').textContent = charCount;
});

// Due date validation and warnings
document.getElementById('due_date').addEventListener('change', function() {
    const selectedDate = new Date(this.value);
    const today = new Date();
    const diffTime = selectedDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const warningDiv = document.getElementById('dateWarning');
    if (warningDiv) {
        warningDiv.remove();
    }
    
    if (diffDays <= 1 && diffDays >= 0) {
        const warning = document.createElement('div');
        warning.id = 'dateWarning';
        warning.className = 'alert alert-warning mt-2';
        warning.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>This assignment is due very soon! Consider setting a higher priority.';
        this.parentElement.appendChild(warning);
        
        // Suggest High priority for urgent assignments
        if (document.getElementById('priority').value !== 'High') {
            document.getElementById('priority').value = 'High';
        }
    }
});

// Priority color coding
document.getElementById('priority').addEventListener('change', function() {
    const prioritySelect = this;
    prioritySelect.className = 'form-select';
    
    if (this.value === 'High') {
        prioritySelect.classList.add('border-danger');
    } else if (this.value === 'Medium') {
        prioritySelect.classList.add('border-warning');
    } else if (this.value === 'Low') {
        prioritySelect.classList.add('border-success');
    }
});

// Initialize priority color on page load
document.addEventListener('DOMContentLoaded', function() {
    const prioritySelect = document.getElementById('priority');
    const event = new Event('change');
    prioritySelect.dispatchEvent(event);
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Warn about unsaved changes
let originalData = {
    title: document.getElementById('title').value,
    subject: document.getElementById('subject').value,
    due_date: document.getElementById('due_date').value,
    priority: document.getElementById('priority').value,
    description: document.getElementById('description').value
};

window.addEventListener('beforeunload', function(e) {
    const currentData = {
        title: document.getElementById('title').value,
        subject: document.getElementById('subject').value,
        due_date: document.getElementById('due_date').value,
        priority: document.getElementById('priority').value,
        description: document.getElementById('description').value
    };
    
    if (JSON.stringify(currentData) !== JSON.stringify(originalData)) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>
{% endblock %}
